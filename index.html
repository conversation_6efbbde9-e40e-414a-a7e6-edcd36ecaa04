<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typing Test - Jermesa Studio</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- <PERSON>ie Consent Banner -->
    <div id="cookieConsent" class="cookie-consent hidden">
        <div class="cookie-content">
            <p>We use cookies to enhance your experience and save your typing test history and settings locally on your device.</p>
            <button id="acceptCookies" class="btn btn-primary">Accept</button>
        </div>
    </div>

    <!-- Navigation Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1>TypingTest</h1>
            </div>
            <nav class="nav-menu">
                <button class="nav-btn active" data-page="test">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                    Test
                </button>
                <button class="nav-btn" data-page="settings">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                    Settings
                </button>
                <button class="nav-btn" data-page="history">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M13,3A9,9 0 0,0 4,12H1L4.89,15.89L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3Z"/>
                    </svg>
                    History
                </button>
                <button id="themeToggle" class="theme-toggle" title="Toggle dark mode">
                    <svg class="theme-icon sun-icon" viewBox="0 0 24 24">
                        <path d="M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M12,2L14.39,5.42C13.65,5.15 12.84,5 12,5C11.16,5 10.35,5.15 9.61,5.42L12,2M3.34,7L7.5,6.65C6.9,7.16 6.36,7.78 5.94,8.5C5.5,9.24 5.25,10 5.11,10.79L3.34,7M3.36,17L5.12,13.23C5.26,14 5.53,14.78 5.95,15.5C6.37,16.24 6.91,16.86 7.5,17.37L3.36,17M20.65,7L18.88,10.79C18.74,10 18.47,9.23 18.05,8.5C17.63,7.78 17.1,7.15 16.5,6.64L20.65,7M20.64,17L16.5,17.36C17.09,16.85 17.62,16.22 18.04,15.5C18.46,14.77 18.73,14 18.87,13.21L20.64,17M12,22L9.59,18.56C10.33,18.83 11.14,19 12,19C12.82,19 13.63,18.83 14.37,18.56L12,22Z"/>
                    </svg>
                    <svg class="theme-icon moon-icon hidden" viewBox="0 0 24 24">
                        <path d="M17.75,4.09L15.22,6.03L16.13,9.09L13.5,7.28L10.87,9.09L11.78,6.03L9.25,4.09L12.44,4L13.5,1L14.56,4L17.75,4.09M21.25,11L19.61,12.25L20.2,14.23L18.5,13.06L16.8,14.23L17.39,12.25L15.75,11L17.81,10.95L18.5,9L19.19,10.95L21.25,11M18.97,15.95C19.8,15.87 20.69,17.05 20.16,17.8C19.84,18.25 19.5,18.67 19.08,19.07C15.17,23 8.84,23 4.94,19.07C1.03,15.17 1.03,8.83 4.94,4.93C5.34,4.53 5.76,4.17 6.21,3.85C6.96,3.32 8.14,4.21 8.06,5.04C7.79,7.9 8.75,10.87 10.95,13.06C13.14,15.26 16.1,16.22 18.97,15.95M17.33,17.97C14.5,17.81 11.7,16.64 9.53,14.5C7.36,12.31 6.2,9.5 6.04,6.68C3.23,9.82 3.34,14.4 6.35,17.41C9.37,20.43 14,20.54 17.33,17.97Z"/>
                    </svg>
                </button>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Typing Test Page -->
        <section id="testPage" class="page active">
            <div class="container">
                <div class="test-container">
                    <!-- Timer Display -->
                    <div class="timer-container">
                        <div class="timer-circle">
                            <svg class="timer-svg" viewBox="0 0 100 100">
                                <circle class="timer-bg" cx="50" cy="50" r="45"/>
                                <circle class="timer-progress" cx="50" cy="50" r="45"/>
                            </svg>
                            <div class="timer-text">
                                <span id="timerDisplay">60</span>
                                <small>seconds</small>
                            </div>
                        </div>
                    </div>

                    <!-- Stats Display -->
                    <div class="stats-container">
                        <div class="stat-item">
                            <span class="stat-value" id="wpmDisplay">0</span>
                            <span class="stat-label">WPM</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="accuracyDisplay">100</span>
                            <span class="stat-label">Accuracy</span>
                        </div>
                    </div>

                    <!-- Text Display -->
                    <div class="text-container">
                        <div id="textDisplay" class="text-display"></div>
                    </div>

                    <!-- Input Area -->
                    <div class="input-container">
                        <textarea id="textInput" class="text-input" placeholder="Click here and start typing..." disabled></textarea>
                    </div>

                    <!-- Control Buttons -->
                    <div class="controls">
                        <button id="startBtn" class="btn btn-primary">Start Test</button>
                        <button id="resetBtn" class="btn btn-secondary">Reset</button>
                        <button id="finishBtn" class="btn btn-accent hidden">Finish Test</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Page -->
        <section id="settingsPage" class="page">
            <div class="container">
                <div class="settings-container">
                    <h2>Settings</h2>
                    
                    <div class="setting-group">
                        <label for="timerDuration">Timer Duration</label>
                        <select id="timerDuration" class="setting-input">
                            <option value="30">30 seconds</option>
                            <option value="60" selected>60 seconds</option>
                            <option value="120">2 minutes</option>
                            <option value="300">5 minutes</option>
                            <option value="custom">Custom</option>
                        </select>
                        <input type="number" id="customTimer" class="setting-input hidden" placeholder="Enter seconds" min="10" max="3600">
                    </div>

                    <div class="setting-group">
                        <label for="customText">Custom Text</label>
                        <textarea id="customText" class="setting-textarea" placeholder="Enter your custom text here, or leave empty to use default paragraphs..."></textarea>
                    </div>

                    <div class="settings-actions">
                        <button id="saveSettings" class="btn btn-primary">Save Settings</button>
                        <button id="resetSettings" class="btn btn-secondary">Reset to Default</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- History Page -->
        <section id="historyPage" class="page">
            <div class="container">
                <div class="history-container">
                    <div class="history-header">
                        <h2>Test History</h2>
                        <div class="history-actions">
                            <button id="exportHistory" class="btn btn-secondary">Export</button>
                            <button id="clearHistory" class="btn btn-danger">Clear All</button>
                        </div>
                    </div>
                    
                    <div class="history-content">
                        <div id="historyList" class="history-list">
                            <div class="history-empty">
                                <p>No test results yet. Complete a typing test to see your history!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Results Modal -->
    <div id="resultsModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Test Results</h3>
                <button class="modal-close" id="closeResults">
                    <svg viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="results-grid">
                    <div class="result-item">
                        <span class="result-value" id="finalWPM">0</span>
                        <span class="result-label">Words Per Minute</span>
                    </div>
                    <div class="result-item">
                        <span class="result-value" id="finalAccuracy">0%</span>
                        <span class="result-label">Accuracy</span>
                    </div>
                    <div class="result-item">
                        <span class="result-value" id="finalTime">0s</span>
                        <span class="result-label">Time Taken</span>
                    </div>
                    <div class="result-item">
                        <span class="result-value" id="finalChars">0</span>
                        <span class="result-label">Characters Typed</span>
                    </div>
                </div>
                <div class="results-details">
                    <p><strong>Correct Characters:</strong> <span id="correctChars">0</span></p>
                    <p><strong>Incorrect Characters:</strong> <span id="incorrectChars">0</span></p>
                    <p><strong>Total Characters:</strong> <span id="totalChars">0</span></p>
                </div>
            </div>
            <div class="modal-footer">
                <button id="retryTest" class="btn btn-primary">Try Again</button>
                <button id="backToMenu" class="btn btn-secondary">Back to Menu</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>Created by <a href="https://www.jermesa.com" target="_blank">Jermesa Studio</a></p>
                <p class="font-attribution">
                    Fonts: <a href="https://fonts.google.com/specimen/Inter" target="_blank">Inter</a>, 
                    <a href="https://fonts.google.com/specimen/JetBrains+Mono" target="_blank">JetBrains Mono</a>, 
                    <a href="https://fonts.google.com/specimen/Poppins" target="_blank">Poppins</a> - 
                    Licensed under <a href="https://scripts.sil.org/OFL" target="_blank">Open Font License</a>
                </p>
                <p><a href="https://jermesa.com/privacy-policy/" target="_blank">Privacy Policy</a></p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
