<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typing Test - Jermesa Studio</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- <PERSON>ie Consent Banner -->
    <div id="cookieConsent" class="cookie-consent hidden">
        <div class="cookie-content">
            <p>We use cookies to enhance your experience and save your typing test history and settings locally on your device.</p>
            <button id="acceptCookies" class="btn btn-primary">Accept</button>
        </div>
    </div>

    <!-- Navigation Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1>TypingTest</h1>
            </div>
            <nav class="nav-menu">
                <button class="nav-btn active" data-page="test">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                    Test
                </button>
                <button class="nav-btn" data-page="settings">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                    Settings
                </button>
                <button class="nav-btn" data-page="history">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M13,3A9,9 0 0,0 4,12H1L4.89,15.89L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3Z"/>
                    </svg>
                    History
                </button>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Typing Test Page -->
        <section id="testPage" class="page active">
            <div class="container">
                <div class="test-container">
                    <!-- Timer Display -->
                    <div class="timer-container">
                        <div class="timer-circle">
                            <svg class="timer-svg" viewBox="0 0 100 100">
                                <circle class="timer-bg" cx="50" cy="50" r="45"/>
                                <circle class="timer-progress" cx="50" cy="50" r="45"/>
                            </svg>
                            <div class="timer-text">
                                <span id="timerDisplay">60</span>
                                <small>seconds</small>
                            </div>
                        </div>
                    </div>

                    <!-- Stats Display -->
                    <div class="stats-container">
                        <div class="stat-item">
                            <span class="stat-value" id="wpmDisplay">0</span>
                            <span class="stat-label">WPM</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="accuracyDisplay">100</span>
                            <span class="stat-label">Accuracy</span>
                        </div>
                    </div>

                    <!-- Text Display -->
                    <div class="text-container">
                        <div id="textDisplay" class="text-display"></div>
                    </div>

                    <!-- Input Area -->
                    <div class="input-container">
                        <textarea id="textInput" class="text-input" placeholder="Click here and start typing..." disabled></textarea>
                    </div>

                    <!-- Control Buttons -->
                    <div class="controls">
                        <button id="startBtn" class="btn btn-primary">Start Test</button>
                        <button id="resetBtn" class="btn btn-secondary">Reset</button>
                        <button id="finishBtn" class="btn btn-accent hidden">Finish Test</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Page -->
        <section id="settingsPage" class="page">
            <div class="container">
                <div class="settings-container">
                    <h2>Settings</h2>
                    
                    <div class="setting-group">
                        <label for="timerDuration">Timer Duration</label>
                        <select id="timerDuration" class="setting-input">
                            <option value="30">30 seconds</option>
                            <option value="60" selected>60 seconds</option>
                            <option value="120">2 minutes</option>
                            <option value="300">5 minutes</option>
                            <option value="custom">Custom</option>
                        </select>
                        <input type="number" id="customTimer" class="setting-input hidden" placeholder="Enter seconds" min="10" max="3600">
                    </div>

                    <div class="setting-group">
                        <label for="customText">Custom Text</label>
                        <textarea id="customText" class="setting-textarea" placeholder="Enter your custom text here, or leave empty to use default paragraphs..."></textarea>
                    </div>

                    <div class="settings-actions">
                        <button id="saveSettings" class="btn btn-primary">Save Settings</button>
                        <button id="resetSettings" class="btn btn-secondary">Reset to Default</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- History Page -->
        <section id="historyPage" class="page">
            <div class="container">
                <div class="history-container">
                    <div class="history-header">
                        <h2>Test History</h2>
                        <div class="history-actions">
                            <button id="exportHistory" class="btn btn-secondary">Export</button>
                            <button id="clearHistory" class="btn btn-danger">Clear All</button>
                        </div>
                    </div>
                    
                    <div class="history-content">
                        <div id="historyList" class="history-list">
                            <div class="history-empty">
                                <p>No test results yet. Complete a typing test to see your history!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Results Modal -->
    <div id="resultsModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Test Results</h3>
                <button class="modal-close" id="closeResults">
                    <svg viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="results-grid">
                    <div class="result-item">
                        <span class="result-value" id="finalWPM">0</span>
                        <span class="result-label">Words Per Minute</span>
                    </div>
                    <div class="result-item">
                        <span class="result-value" id="finalAccuracy">0%</span>
                        <span class="result-label">Accuracy</span>
                    </div>
                    <div class="result-item">
                        <span class="result-value" id="finalTime">0s</span>
                        <span class="result-label">Time Taken</span>
                    </div>
                    <div class="result-item">
                        <span class="result-value" id="finalChars">0</span>
                        <span class="result-label">Characters Typed</span>
                    </div>
                </div>
                <div class="results-details">
                    <p><strong>Correct Characters:</strong> <span id="correctChars">0</span></p>
                    <p><strong>Incorrect Characters:</strong> <span id="incorrectChars">0</span></p>
                    <p><strong>Total Characters:</strong> <span id="totalChars">0</span></p>
                </div>
            </div>
            <div class="modal-footer">
                <button id="retryTest" class="btn btn-primary">Try Again</button>
                <button id="backToMenu" class="btn btn-secondary">Back to Menu</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>Created by <a href="https://www.jermesa.com" target="_blank">Jermesa Studio</a></p>
                <p class="font-attribution">
                    Fonts: <a href="https://fonts.google.com/specimen/Inter" target="_blank">Inter</a>, 
                    <a href="https://fonts.google.com/specimen/JetBrains+Mono" target="_blank">JetBrains Mono</a>, 
                    <a href="https://fonts.google.com/specimen/Poppins" target="_blank">Poppins</a> - 
                    Licensed under <a href="https://scripts.sil.org/OFL" target="_blank">Open Font License</a>
                </p>
                <p><a href="https://jermesa.com/privacy-policy/" target="_blank">Privacy Policy</a></p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
