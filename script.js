// Typing Test Application
class TypingTest {
    constructor() {
        this.currentText = '';
        this.userInput = '';
        this.startTime = null;
        this.endTime = null;
        this.isTestActive = false;
        this.currentPosition = 0;
        this.correctChars = 0;
        this.incorrectChars = 0;
        this.timerInterval = null;
        this.timeRemaining = 60;
        this.timerDuration = 60;
        this.wpmInterval = null;
        
        // Default paragraphs
        this.defaultTexts = [
            "The quick brown fox jumps over the lazy dog. This pangram contains every letter of the alphabet at least once, making it perfect for typing practice. It has been used for decades to test typewriters and keyboards.",
            "In the digital age, typing skills have become increasingly important. Whether you're writing emails, coding, or creating documents, the ability to type quickly and accurately can significantly improve your productivity and efficiency.",
            "Practice makes perfect when it comes to typing. Regular practice sessions, proper finger placement, and maintaining good posture are essential elements for developing excellent typing skills that will serve you well throughout your career.",
            "Technology continues to evolve at a rapid pace, transforming the way we work, communicate, and live. From artificial intelligence to quantum computing, these innovations are reshaping our world in unprecedented ways.",
            "The art of effective communication extends beyond just speaking and writing. It involves active listening, empathy, and the ability to convey complex ideas in simple, understandable terms that resonate with your audience."
        ];
        
        this.settings = this.loadSettings();
        this.history = this.loadHistory();
        
        this.initializeElements();
        this.bindEvents();
        this.initializePage();
    }
    
    initializeElements() {
        // Navigation elements
        this.navButtons = document.querySelectorAll('.nav-btn');
        this.pages = document.querySelectorAll('.page');
        
        // Test elements
        this.textDisplay = document.getElementById('textDisplay');
        this.textInput = document.getElementById('textInput');
        this.startBtn = document.getElementById('startBtn');
        this.resetBtn = document.getElementById('resetBtn');
        this.finishBtn = document.getElementById('finishBtn');
        this.wpmDisplay = document.getElementById('wpmDisplay');
        this.accuracyDisplay = document.getElementById('accuracyDisplay');
        this.timerDisplay = document.getElementById('timerDisplay');
        this.timerProgress = document.querySelector('.timer-progress');
        
        // Settings elements
        this.timerDurationSelect = document.getElementById('timerDuration');
        this.customTimerInput = document.getElementById('customTimer');
        this.customTextArea = document.getElementById('customText');
        this.saveSettingsBtn = document.getElementById('saveSettings');
        this.resetSettingsBtn = document.getElementById('resetSettings');
        
        // History elements
        this.historyList = document.getElementById('historyList');
        this.exportHistoryBtn = document.getElementById('exportHistory');
        this.clearHistoryBtn = document.getElementById('clearHistory');
        
        // Results modal elements
        this.resultsModal = document.getElementById('resultsModal');
        this.closeResultsBtn = document.getElementById('closeResults');
        this.retryTestBtn = document.getElementById('retryTest');
        this.backToMenuBtn = document.getElementById('backToMenu');
        this.finalWPM = document.getElementById('finalWPM');
        this.finalAccuracy = document.getElementById('finalAccuracy');
        this.finalTime = document.getElementById('finalTime');
        this.finalChars = document.getElementById('finalChars');
        this.correctCharsSpan = document.getElementById('correctChars');
        this.incorrectCharsSpan = document.getElementById('incorrectChars');
        this.totalCharsSpan = document.getElementById('totalChars');
        
        // Cookie consent
        this.cookieConsent = document.getElementById('cookieConsent');
        this.acceptCookiesBtn = document.getElementById('acceptCookies');

        // Theme toggle
        this.themeToggle = document.getElementById('themeToggle');
        this.sunIcon = this.themeToggle.querySelector('.sun-icon');
        this.moonIcon = this.themeToggle.querySelector('.moon-icon');
    }
    
    bindEvents() {
        // Navigation events
        this.navButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const page = e.currentTarget.dataset.page;
                this.switchPage(page);
            });
        });
        
        // Test events
        this.startBtn.addEventListener('click', () => this.startTest());
        this.resetBtn.addEventListener('click', () => this.resetTest());
        this.finishBtn.addEventListener('click', () => this.finishTest());
        this.textInput.addEventListener('input', (e) => this.handleInput(e));
        this.textInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // Settings events
        this.timerDurationSelect.addEventListener('change', () => this.handleTimerDurationChange());
        this.saveSettingsBtn.addEventListener('click', () => this.saveSettings());
        this.resetSettingsBtn.addEventListener('click', () => this.resetSettings());
        
        // History events
        this.exportHistoryBtn.addEventListener('click', () => this.exportHistory());
        this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());
        
        // Results modal events
        this.closeResultsBtn.addEventListener('click', () => this.closeResultsModal());
        this.retryTestBtn.addEventListener('click', () => this.retryTest());
        this.backToMenuBtn.addEventListener('click', () => this.backToMenu());
        
        // Cookie consent events
        this.acceptCookiesBtn.addEventListener('click', () => this.acceptCookies());

        // Theme toggle events
        this.themeToggle.addEventListener('click', () => this.toggleTheme());
        
        // Modal backdrop click
        this.resultsModal.addEventListener('click', (e) => {
            if (e.target === this.resultsModal) {
                this.closeResultsModal();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeResultsModal();
            }
        });
    }
    
    initializePage() {
        this.loadText();
        this.updateTimerDisplay();
        this.loadSettingsToUI();
        this.loadHistoryToUI();
        this.checkCookieConsent();
        this.initializeTheme();
    }
    
    // Navigation Methods
    switchPage(pageName) {
        // Update navigation buttons
        this.navButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.page === pageName);
        });
        
        // Update pages
        this.pages.forEach(page => {
            page.classList.toggle('active', page.id === `${pageName}Page`);
        });
        
        // Reset test if switching away from test page
        if (pageName !== 'test' && this.isTestActive) {
            this.resetTest();
        }
        
        // Refresh history if switching to history page
        if (pageName === 'history') {
            this.loadHistoryToUI();
        }
    }
    
    // Test Methods
    loadText() {
        if (this.settings.customText && this.settings.customText.trim()) {
            this.currentText = this.settings.customText.trim();
        } else {
            const randomIndex = Math.floor(Math.random() * this.defaultTexts.length);
            this.currentText = this.defaultTexts[randomIndex];
        }
        
        this.displayText();
    }
    
    displayText() {
        this.textDisplay.innerHTML = '';
        for (let i = 0; i < this.currentText.length; i++) {
            const span = document.createElement('span');
            span.textContent = this.currentText[i];
            span.classList.add('char');
            if (i === 0) span.classList.add('current');
            this.textDisplay.appendChild(span);
        }
    }
    
    startTest() {
        this.isTestActive = true;
        this.startTime = new Date();
        this.currentPosition = 0;
        this.correctChars = 0;
        this.incorrectChars = 0;
        this.userInput = '';
        this.timeRemaining = this.timerDuration;
        
        // Update UI
        this.textInput.disabled = false;
        this.textInput.focus();
        this.textInput.value = '';
        this.startBtn.classList.add('hidden');
        this.finishBtn.classList.remove('hidden');
        
        // Start timers
        this.startTimer();
        this.startWPMCalculation();
        
        // Reset display
        this.displayText();
        this.updateStats();
    }
    
    resetTest() {
        this.isTestActive = false;
        this.startTime = null;
        this.endTime = null;
        this.currentPosition = 0;
        this.correctChars = 0;
        this.incorrectChars = 0;
        this.userInput = '';
        this.timeRemaining = this.timerDuration;
        
        // Clear intervals
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
        if (this.wpmInterval) {
            clearInterval(this.wpmInterval);
            this.wpmInterval = null;
        }
        
        // Update UI
        this.textInput.disabled = true;
        this.textInput.value = '';
        this.startBtn.classList.remove('hidden');
        this.finishBtn.classList.add('hidden');
        
        // Reset displays
        this.loadText();
        this.updateTimerDisplay();
        this.wpmDisplay.textContent = '0';
        this.accuracyDisplay.textContent = '100';
        this.updateTimerProgress();
    }
    
    finishTest() {
        if (!this.isTestActive) return;
        
        this.endTime = new Date();
        this.isTestActive = false;
        
        // Clear intervals
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
        if (this.wpmInterval) {
            clearInterval(this.wpmInterval);
            this.wpmInterval = null;
        }
        
        // Calculate final results
        const results = this.calculateResults();
        
        // Save to history
        this.saveTestResult(results);
        
        // Show results modal
        this.showResultsModal(results);
        
        // Update UI
        this.textInput.disabled = true;
        this.startBtn.classList.remove('hidden');
        this.finishBtn.classList.add('hidden');
    }
    
    handleInput(e) {
        if (!this.isTestActive) return;
        
        this.userInput = e.target.value;
        this.updateTextDisplay();
        this.updateStats();
    }
    
    handleKeyDown(e) {
        if (!this.isTestActive) return;
        
        // Prevent certain keys that might interfere
        if (e.key === 'Tab') {
            e.preventDefault();
        }
    }
    
    updateTextDisplay() {
        const chars = this.textDisplay.querySelectorAll('.char');
        
        chars.forEach((char, index) => {
            char.classList.remove('correct', 'incorrect', 'current');
            
            if (index < this.userInput.length) {
                if (this.userInput[index] === this.currentText[index]) {
                    char.classList.add('correct');
                } else {
                    char.classList.add('incorrect');
                }
            } else if (index === this.userInput.length) {
                char.classList.add('current');
            }
        });
        
        this.currentPosition = this.userInput.length;
        
        // Check if test is complete
        if (this.userInput.length >= this.currentText.length) {
            this.finishTest();
        }
    }
    
    updateStats() {
        // Calculate correct and incorrect characters
        this.correctChars = 0;
        this.incorrectChars = 0;
        
        for (let i = 0; i < this.userInput.length; i++) {
            if (this.userInput[i] === this.currentText[i]) {
                this.correctChars++;
            } else {
                this.incorrectChars++;
            }
        }
        
        // Calculate accuracy
        const totalTyped = this.userInput.length;
        const accuracy = totalTyped > 0 ? Math.round((this.correctChars / totalTyped) * 100) : 100;
        this.accuracyDisplay.textContent = accuracy;
        
        // WPM is calculated in separate interval for smooth updates
    }
    
    calculateWPM() {
        if (!this.startTime || !this.isTestActive) return 0;
        
        const currentTime = new Date();
        const timeElapsed = (currentTime - this.startTime) / 1000 / 60; // in minutes
        
        if (timeElapsed === 0) return 0;
        
        // Standard WPM calculation: (characters typed / 5) / time in minutes
        const wordsTyped = this.correctChars / 5;
        return Math.round(wordsTyped / timeElapsed);
    }
    
    startTimer() {
        this.updateTimerDisplay();
        this.updateTimerProgress();
        
        this.timerInterval = setInterval(() => {
            this.timeRemaining--;
            this.updateTimerDisplay();
            this.updateTimerProgress();
            
            if (this.timeRemaining <= 0) {
                this.finishTest();
            }
        }, 1000);
    }
    
    startWPMCalculation() {
        this.wpmInterval = setInterval(() => {
            if (this.isTestActive) {
                const wpm = this.calculateWPM();
                this.wpmDisplay.textContent = wpm;
            }
        }, 100); // Update every 100ms for smooth display
    }
    
    updateTimerDisplay() {
        this.timerDisplay.textContent = this.timeRemaining;
    }
    
    updateTimerProgress() {
        const progress = (this.timerDuration - this.timeRemaining) / this.timerDuration;
        const circumference = 2 * Math.PI * 45; // radius is 45
        const offset = circumference * (1 - progress);
        this.timerProgress.style.strokeDashoffset = offset;
    }
    
    calculateResults() {
        const timeElapsed = this.endTime ? (this.endTime - this.startTime) / 1000 : this.timerDuration;
        const totalChars = this.userInput.length;
        const accuracy = totalChars > 0 ? Math.round((this.correctChars / totalChars) * 100) : 100;
        const wpm = timeElapsed > 0 ? Math.round((this.correctChars / 5) / (timeElapsed / 60)) : 0;
        
        return {
            wpm,
            accuracy,
            timeElapsed: Math.round(timeElapsed),
            correctChars: this.correctChars,
            incorrectChars: this.incorrectChars,
            totalChars,
            textPreview: this.currentText.substring(0, 50) + (this.currentText.length > 50 ? '...' : ''),
            timestamp: new Date().toISOString(),
            timerDuration: this.timerDuration
        };
    }
    
    // Settings Methods
    loadSettings() {
        const defaultSettings = {
            timerDuration: 60,
            customText: ''
        };
        
        try {
            const saved = localStorage.getItem('typingTestSettings');
            return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
        } catch (error) {
            console.error('Error loading settings:', error);
            return defaultSettings;
        }
    }
    
    saveSettings() {
        const timerValue = this.timerDurationSelect.value;
        let duration = parseInt(timerValue);
        
        if (timerValue === 'custom') {
            duration = parseInt(this.customTimerInput.value) || 60;
        }
        
        this.settings = {
            timerDuration: duration,
            customText: this.customTextArea.value.trim()
        };
        
        try {
            localStorage.setItem('typingTestSettings', JSON.stringify(this.settings));
            this.timerDuration = duration;
            this.timeRemaining = duration;
            this.updateTimerDisplay();
            this.updateTimerProgress();
            this.loadText();
            
            // Show success feedback
            this.showNotification('Settings saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showNotification('Error saving settings', 'error');
        }
    }
    
    resetSettings() {
        this.settings = {
            timerDuration: 60,
            customText: ''
        };
        
        try {
            localStorage.removeItem('typingTestSettings');
            this.timerDuration = 60;
            this.timeRemaining = 60;
            this.loadSettingsToUI();
            this.updateTimerDisplay();
            this.updateTimerProgress();
            this.loadText();
            
            this.showNotification('Settings reset to default', 'success');
        } catch (error) {
            console.error('Error resetting settings:', error);
            this.showNotification('Error resetting settings', 'error');
        }
    }
    
    loadSettingsToUI() {
        // Load timer duration
        if ([30, 60, 120, 300].includes(this.settings.timerDuration)) {
            this.timerDurationSelect.value = this.settings.timerDuration;
            this.customTimerInput.classList.add('hidden');
        } else {
            this.timerDurationSelect.value = 'custom';
            this.customTimerInput.value = this.settings.timerDuration;
            this.customTimerInput.classList.remove('hidden');
        }
        
        // Load custom text
        this.customTextArea.value = this.settings.customText;
        
        // Update timer duration
        this.timerDuration = this.settings.timerDuration;
        this.timeRemaining = this.settings.timerDuration;
    }
    
    handleTimerDurationChange() {
        if (this.timerDurationSelect.value === 'custom') {
            this.customTimerInput.classList.remove('hidden');
            this.customTimerInput.focus();
        } else {
            this.customTimerInput.classList.add('hidden');
        }
    }

    // History Methods
    loadHistory() {
        try {
            const saved = localStorage.getItem('typingTestHistory');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('Error loading history:', error);
            return [];
        }
    }

    saveTestResult(result) {
        try {
            this.history.unshift(result); // Add to beginning of array

            // Keep only last 100 results
            if (this.history.length > 100) {
                this.history = this.history.slice(0, 100);
            }

            localStorage.setItem('typingTestHistory', JSON.stringify(this.history));
        } catch (error) {
            console.error('Error saving test result:', error);
        }
    }

    loadHistoryToUI() {
        if (this.history.length === 0) {
            this.historyList.innerHTML = `
                <div class="history-empty">
                    <p>No test results yet. Complete a typing test to see your history!</p>
                </div>
            `;
            return;
        }

        this.historyList.innerHTML = '';

        this.history.forEach((result, index) => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';

            const date = new Date(result.timestamp);
            const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

            historyItem.innerHTML = `
                <div class="history-date">${formattedDate}</div>
                <div class="history-stat">${result.wpm} WPM</div>
                <div class="history-stat">${result.accuracy}%</div>
                <div class="history-stat">${result.timerDuration}s</div>
                <div class="history-preview">${result.textPreview}</div>
            `;

            this.historyList.appendChild(historyItem);
        });
    }

    exportHistory() {
        if (this.history.length === 0) {
            this.showNotification('No history to export', 'warning');
            return;
        }

        try {
            // Create CSV content
            const headers = ['Date', 'Time', 'WPM', 'Accuracy', 'Timer Duration', 'Correct Chars', 'Incorrect Chars', 'Total Chars', 'Text Preview'];
            const csvContent = [
                headers.join(','),
                ...this.history.map(result => {
                    const date = new Date(result.timestamp);
                    return [
                        date.toLocaleDateString(),
                        date.toLocaleTimeString(),
                        result.wpm,
                        result.accuracy + '%',
                        result.timerDuration + 's',
                        result.correctChars,
                        result.incorrectChars,
                        result.totalChars,
                        '"' + result.textPreview.replace(/"/g, '""') + '"'
                    ].join(',');
                })
            ].join('\n');

            // Create and download file
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `typing-test-history-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            this.showNotification('History exported successfully!', 'success');
        } catch (error) {
            console.error('Error exporting history:', error);
            this.showNotification('Error exporting history', 'error');
        }
    }

    clearHistory() {
        if (this.history.length === 0) {
            this.showNotification('No history to clear', 'warning');
            return;
        }

        if (confirm('Are you sure you want to clear all test history? This action cannot be undone.')) {
            try {
                this.history = [];
                localStorage.removeItem('typingTestHistory');
                this.loadHistoryToUI();
                this.showNotification('History cleared successfully', 'success');
            } catch (error) {
                console.error('Error clearing history:', error);
                this.showNotification('Error clearing history', 'error');
            }
        }
    }

    // Results Modal Methods
    showResultsModal(results) {
        this.finalWPM.textContent = results.wpm;
        this.finalAccuracy.textContent = results.accuracy + '%';
        this.finalTime.textContent = results.timeElapsed + 's';
        this.finalChars.textContent = results.totalChars;
        this.correctCharsSpan.textContent = results.correctChars;
        this.incorrectCharsSpan.textContent = results.incorrectChars;
        this.totalCharsSpan.textContent = results.totalChars;

        this.resultsModal.classList.remove('hidden');

        // Focus management for accessibility
        this.closeResultsBtn.focus();
    }

    closeResultsModal() {
        this.resultsModal.classList.add('hidden');
    }

    retryTest() {
        this.closeResultsModal();
        this.resetTest();
        setTimeout(() => this.startTest(), 100);
    }

    backToMenu() {
        this.closeResultsModal();
        this.resetTest();
    }

    // Cookie Consent Methods
    checkCookieConsent() {
        try {
            const consent = localStorage.getItem('cookieConsent');
            if (!consent) {
                this.cookieConsent.classList.remove('hidden');
            }
        } catch (error) {
            // If localStorage is not available, don't show cookie banner
            console.warn('localStorage not available for cookie consent');
        }
    }

    acceptCookies() {
        try {
            localStorage.setItem('cookieConsent', 'accepted');
            this.cookieConsent.classList.add('hidden');
        } catch (error) {
            console.error('Error saving cookie consent:', error);
            // Hide banner anyway if localStorage fails
            this.cookieConsent.classList.add('hidden');
        }
    }

    // Theme Methods
    initializeTheme() {
        try {
            const savedTheme = localStorage.getItem('typingTestTheme') || 'light';
            this.setTheme(savedTheme);
        } catch (error) {
            console.error('Error loading theme:', error);
            this.setTheme('light');
        }
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);

        try {
            localStorage.setItem('typingTestTheme', newTheme);
        } catch (error) {
            console.error('Error saving theme:', error);
        }
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);

        // Update theme toggle icons
        if (theme === 'dark') {
            this.sunIcon.classList.add('hidden');
            this.moonIcon.classList.remove('hidden');
            this.themeToggle.title = 'Switch to light mode';
        } else {
            this.sunIcon.classList.remove('hidden');
            this.moonIcon.classList.add('hidden');
            this.themeToggle.title = 'Switch to dark mode';
        }
    }

    // Utility Methods
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Add styles
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontWeight: '500',
            zIndex: '1001',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease-in-out',
            maxWidth: '300px',
            wordWrap: 'break-word'
        });

        // Set background color based on type
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        // Add to DOM
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);

        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TypingTest();
});
