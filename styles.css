/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #6b7280;
    --accent-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;

    /* Light Theme Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --bg-dark: #1e293b;

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-light: #ffffff;

    /* Border Colors */
    --border-color: #e2e8f0;
    --border-focus: #3b82f6;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
    --font-display: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme */
[data-theme="dark"] {
    /* Background Colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-dark: #000000;

    /* Text Colors */
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-light: #ffffff;

    /* Border Colors */
    --border-color: #334155;
    --border-focus: #3b82f6;

    /* Shadows for dark mode */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

body {
    font-family: var(--font-primary);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.active {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }

    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .timer-progress {
        transition: none !important;
    }
}

/* Cookie Consent Banner */
.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-dark);
    color: var(--text-light);
    padding: var(--spacing-md);
    z-index: 1000;
    transform: translateY(100%);
    transition: transform var(--transition-normal);
}

.cookie-consent:not(.hidden) {
    transform: translateY(0);
}

.cookie-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    gap: var(--spacing-md);
}

.cookie-content p {
    margin: 0;
    flex: 1;
}

/* Header Navigation */
.header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand h1 {
    font-family: var(--font-display);
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.75rem;
}

.nav-menu {
    display: flex;
    gap: var(--spacing-sm);
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: transparent;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.nav-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.nav-btn.active {
    background: var(--primary-color);
    color: var(--text-light);
    border-color: var(--primary-color);
}

.nav-icon {
    width: 18px;
    height: 18px;
    fill: currentColor;
}

/* Theme Toggle */
.theme-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-left: var(--spacing-sm);
}

.theme-toggle:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

.theme-icon {
    width: 20px;
    height: 20px;
    fill: var(--text-secondary);
    transition: all var(--transition-fast);
}

.theme-toggle:hover .theme-icon {
    fill: var(--primary-color);
}

/* Main Content */
.main {
    flex: 1;
    padding: var(--spacing-xl) 0;
    position: relative;
}

.page {
    display: none;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
}

.page.active {
    display: block;
    opacity: 1;
    visibility: visible;
    position: relative;
}

/* Test Page Styles */
.test-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

/* Timer Styles */
.timer-container {
    margin-bottom: var(--spacing-xl);
}

.timer-circle {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
}

.timer-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.timer-bg {
    fill: none;
    stroke: var(--bg-tertiary);
    stroke-width: 4;
}

.timer-progress {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 4;
    stroke-linecap: round;
    stroke-dasharray: 283;
    stroke-dashoffset: 0;
    transition: stroke-dashoffset var(--transition-fast);
}

.timer-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.timer-text span {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.timer-text small {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Stats Display */
.stats-container {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    font-family: var(--font-mono);
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: var(--spacing-xs);
}

/* Text Display */
.text-container {
    margin-bottom: var(--spacing-xl);
}

.text-display {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    font-family: var(--font-mono);
    font-size: 1.125rem;
    line-height: 1.8;
    text-align: left;
    min-height: 200px;
    position: relative;
    overflow-wrap: break-word;
}

.text-display .char {
    position: relative;
}

.text-display .char.correct {
    background-color: rgba(16, 185, 129, 0.2);
    color: var(--accent-color);
}

.text-display .char.incorrect {
    background-color: rgba(239, 68, 68, 0.2);
    color: var(--danger-color);
}

.text-display .char.current {
    background-color: var(--primary-color);
    color: var(--text-light);
    animation: blink 1s infinite;
}

/* Dark mode character highlighting adjustments */
[data-theme="dark"] .text-display .char.correct {
    background-color: rgba(16, 185, 129, 0.3);
}

[data-theme="dark"] .text-display .char.incorrect {
    background-color: rgba(239, 68, 68, 0.3);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Input Area */
.input-container {
    margin-bottom: var(--spacing-xl);
}

.text-input {
    width: 100%;
    min-height: 120px;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: var(--font-mono);
    font-size: 1rem;
    line-height: 1.6;
    resize: vertical;
    transition: border-color var(--transition-fast);
}

.text-input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.text-input:disabled {
    background-color: var(--bg-tertiary);
    cursor: not-allowed;
    opacity: 0.6;
}

/* Controls */
.controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    background: transparent;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-light);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
}

.btn-accent {
    background: var(--accent-color);
    color: var(--text-light);
    border-color: var(--accent-color);
}

.btn-accent:hover {
    background: #059669;
    border-color: #059669;
}

.btn-danger {
    background: var(--danger-color);
    color: var(--text-light);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Settings Page */
.settings-container {
    max-width: 600px;
    margin: 0 auto;
}

.settings-container h2 {
    font-family: var(--font-display);
    font-size: 2rem;
    margin-bottom: var(--spacing-xl);
    text-align: center;
    color: var(--text-primary);
}

.setting-group {
    margin-bottom: var(--spacing-lg);
}

.setting-group label {
    display: block;
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.setting-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: border-color var(--transition-fast);
}

.setting-input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.setting-textarea {
    width: 100%;
    min-height: 150px;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.6;
    resize: vertical;
    transition: border-color var(--transition-fast);
}

.setting-textarea:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.settings-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-xl);
}

/* History Page */
.history-container {
    max-width: 900px;
    margin: 0 auto;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.history-header h2 {
    font-family: var(--font-display);
    font-size: 2rem;
    color: var(--text-primary);
}

.history-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.history-list {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.history-empty {
    padding: var(--spacing-2xl);
    text-align: center;
    color: var(--text-secondary);
}

.history-item {
    display: grid;
    grid-template-columns: 1fr auto auto auto auto;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    align-items: center;
    transition: background-color var(--transition-fast);
}

.history-item:hover {
    background-color: var(--bg-primary);
}

.history-item:last-child {
    border-bottom: none;
}

.history-date {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.history-stat {
    text-align: center;
    font-family: var(--font-mono);
    font-weight: 500;
}

.history-preview {
    font-size: 0.75rem;
    color: var(--text-muted);
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal:not(.hidden) {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal:not(.hidden) .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-family: var(--font-display);
    font-size: 1.25rem;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-secondary);
}

.modal-close svg {
    width: 20px;
    height: 20px;
    fill: var(--text-secondary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.result-item {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.result-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    font-family: var(--font-mono);
}

.result-label {
    display: block;
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: var(--spacing-xs);
}

.results-details {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
}

.results-details p {
    margin-bottom: var(--spacing-xs);
}

.results-details p:last-child {
    margin-bottom: 0;
}

.modal-footer {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* SEO Content Sections */
.seo-content {
    background: var(--bg-primary);
    padding: var(--spacing-2xl) 0;
    border-top: 1px solid var(--border-color);
}

.content-section {
    margin-bottom: var(--spacing-2xl);
}

.content-section:last-child {
    margin-bottom: 0;
}

.content-section h2 {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.3;
}

.content-section h3 {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
}

.content-section p {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.content-section p:last-child {
    margin-bottom: 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.feature-item {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-fast);
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.feature-item h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.feature-item p {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.instructions-list {
    list-style: none;
    counter-reset: step-counter;
    padding-left: 0;
    margin-top: var(--spacing-lg);
}

.instructions-list li {
    counter-increment: step-counter;
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-xl);
    position: relative;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-secondary);
}

.instructions-list li::before {
    content: counter(step-counter);
    position: absolute;
    left: 0;
    top: 0;
    background: var(--primary-color);
    color: var(--text-light);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
}

.benefits-list {
    list-style: none;
    padding-left: 0;
    margin-top: var(--spacing-lg);
}

.benefits-list li {
    margin-bottom: var(--spacing-sm);
    padding-left: var(--spacing-lg);
    position: relative;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-secondary);
}

.benefits-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--accent-color);
    font-weight: bold;
    font-size: 1.1rem;
}

/* FAQ Section */
.faq-section {
    background: var(--bg-secondary);
    padding: var(--spacing-2xl) 0;
    border-top: 1px solid var(--border-color);
}

.faq-section h2 {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.faq-item {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.faq-item h3 {
    font-family: var(--font-display);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
}

.faq-item p {
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 0;
}

/* Test Header */
.test-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.test-header h1 {
    font-family: var(--font-display);
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.2;
}

.test-description {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg) 0;
    margin-top: auto;
}

.footer-content {
    text-align: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.footer-content p {
    margin-bottom: var(--spacing-sm);
}

.footer-content p:last-child {
    margin-bottom: 0;
}

.footer-content a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-content a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .nav-menu {
        gap: var(--spacing-xs);
    }
    
    .nav-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.75rem;
    }
    
    .nav-icon {
        width: 16px;
        height: 16px;
    }

    .theme-toggle {
        width: 36px;
        height: 36px;
        margin-left: var(--spacing-xs);
    }

    .theme-icon {
        width: 18px;
        height: 18px;
    }
    
    .stats-container {
        gap: var(--spacing-md);
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .text-display {
        padding: var(--spacing-md);
        font-size: 1rem;
        min-height: 150px;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }
    
    .settings-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .history-header {
        flex-direction: column;
        text-align: center;
    }
    
    .history-item {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .timer-circle {
        width: 100px;
        height: 100px;
    }
    
    .timer-text span {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .main {
        padding: var(--spacing-md) 0;
    }
    
    .nav-brand h1 {
        font-size: 1.5rem;
    }
    
    .text-display {
        font-size: 0.875rem;
    }
    
    .modal-content {
        width: 95%;
    }
    
    .history-preview {
        max-width: none;
        white-space: normal;
        overflow: visible;
        text-overflow: initial;
    }
}
